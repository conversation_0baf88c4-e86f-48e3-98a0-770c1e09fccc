# CheeStack (芝士堆)

> 基于FSRS算法的智能学习工具 - 让学习更高效，记忆更持久

## 🎯 项目概述

**CheeStack (芝士堆)** 是一款基于FSRS算法的智能学习应用，专注于帮助用户高效记忆和掌握各类知识点。无论是语言学习、考试备考，还是专业知识积累，芝士堆都能通过科学的间隔重复算法，让学习更高效、记忆更持久。

**技术栈**: FastAPI + Flutter + PostgreSQL + Redis + Sherpa-ONNX
**项目状态**: 活跃开发中
**最后更新**: 2025年1月

### 产品定位
- 📚 **全学科覆盖** - 支持语言、数学、历史、地理、科学等各领域知识
- 🎯 **考试备考** - 高考、四六级、托福雅思、公务员等各类考试的最佳伙伴
- 💼 **职业技能** - 编程、医学、法律、金融等专业知识学习
- 🌟 **兴趣学习** - 天文地理、艺术文化等兴趣知识积累

### 核心功能
- 🧠 **科学记忆算法** - 基于FSRS算法的智能复习调度
- 📚 **全学科知识管理** - 支持各个学科和领域的知识学习和组织
- 🎵 **多媒体学习卡片** - 支持文字、图片、音频、视频的丰富学习内容
- 🗣️ **语音学习功能** - 集成Sherpa-ONNX实现多语言离线语音识别和发音评估
- 🎯 **考试备考模式** - 模拟考试、专项训练、错题集等考试功能
- 📊 **学习进度分析** - 详细的学习统计、记忆曲线和掌握度分析
- 👥 **用户系统** - 完整的认证、权限和多设备数据同步
- 📱 **离线优先设计** - 本地数据优先，网络断开时仍可正常使用

## 🚀 快速开始

### 环境要求
- **后端**: Python >= 3.12, PostgreSQL >= 13, Redis >= 6.0
- **前端**: Flutter >= 3.0, Dart >= 3.0
- **系统**: macOS/Linux/Windows (推荐macOS/Linux)

### 安装运行

#### 1. 克隆项目
```bash
git clone https://github.com/yourusername/cheestack.git
cd cheestack
```

#### 2. 后端启动
```bash
cd cheestack-fastapi
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 3. 前端启动
```bash
cd cheestack-flt
flutter pub get
flutter run
```

### 基本使用
1. **注册账号** - 通过手机号或邮箱注册
2. **创建书籍** - 组织学习内容到不同书籍中
3. **添加卡片** - 创建包含问题、答案和多媒体资源的学习卡片
4. **开始学习** - 系统智能安排学习和复习计划
5. **语音练习** - 使用语音识别功能进行口语练习

## 🔧 技术特性

### 离线优先架构
- **本地数据优先** - 应用优先使用本地SQLite数据库，确保无网络时正常使用
- **智能数据同步** - 网络恢复时自动同步本地和云端数据
- **优雅降级** - 网络异常时自动切换到本地模式，用户体验无缝

### 数据管理策略
- **创作统计** - 从本地数据库实时计算书籍、卡片数量和创作统计
- **学习记录** - 本地存储学习进度，支持离线学习和复习
- **用户配置** - 本地缓存用户设置和偏好配置

### 性能优化
- **懒加载** - 按需加载数据，减少启动时间
- **缓存策略** - 多层缓存机制，提升响应速度
- **异步处理** - 非阻塞数据操作，保持UI流畅

## 📁 项目结构

```
cheestack/
├── README.md                    # 项目总览 (本文件)
├── docs/                        # 📚 文档中心
│   ├── README.md               # 文档导航
│   ├── architecture/           # 架构设计文档
│   ├── api/                    # API参考文档
│   ├── development/            # 开发指南
│   ├── deployment/             # 部署文档
│   └── user-guides/            # 用户指南
│
├── cheestack-fastapi/          # 🔧 后端项目 (FastAPI)
│   ├── README.md               # 后端项目说明
│   ├── main.py                 # 应用入口
│   ├── core/                   # 核心功能模块
│   ├── apps/                   # 业务应用模块
│   │   ├── auth/               # 用户认证
│   │   ├── study/              # 学习系统 (原版)
│   │   ├── learn/              # 学习系统 (优化版)
│   │   ├── general/            # 通用功能
│   │   └── pronunciation/      # 发音功能
│   ├── sutils/                 # 工具函数库
│   ├── requirements.txt        # Python依赖
│   └── pyproject.toml          # 项目配置
│
├── cheestack-flt/              # 📱 前端项目 (Flutter)
│   ├── README.md               # 前端项目说明
│   ├── lib/                    # 源代码目录
│   │   ├── pages/              # 页面组件
│   │   ├── controllers/        # 状态管理
│   │   ├── models/             # 数据模型
│   │   ├── apis/               # API接口
│   │   ├── services/           # 业务服务
│   │   └── common/             # 通用组件
│   ├── assets/                 # 静态资源
│   ├── pubspec.yaml            # Flutter依赖配置
│   └── docs/                   # 前端专用文档
│
└── .augment/                   # 🤖 AI开发规则
    └── rules/                  # 开发规范文件
```

## 📚 文档

### 📖 完整文档架构
- [📖 文档中心](./docs/README.md) - 文档导航和架构说明

### 📋 三大文档类别
- [📋 需求文档](./docs/requirements/README.md) - 产品需求和功能规格
- [🎨 设计文档](./docs/design/README.md) - 系统设计和架构文档
- [📝 任务列表](./docs/tasks/README.md) - 项目任务和进度管理

### 🔧 技术文档
- [🔌 API文档](./docs/api/README.md) - 接口参考文档
- [🚀 部署指南](./docs/deployment/README.md) - 环境搭建和部署
- [👨‍💻 开发指南](./docs/development/README.md) - 开发规范和最佳实践

### 子项目文档
- [后端文档](./cheestack-fastapi/README.md) - FastAPI项目详细说明
- [前端文档](./cheestack-flt/README.md) - Flutter项目详细说明

## 🏗️ 核心技术

### 后端技术栈
- **Web框架**: FastAPI 0.111.1 - 高性能异步API框架
- **数据库ORM**: Tortoise ORM - 异步Python ORM
- **数据库**: PostgreSQL - 主数据库, Redis - 缓存和会话
- **认证**: JWT + 手机验证码认证
- **文件存储**: 腾讯云COS对象存储
- **AI服务**: 集成多种AI服务 (TTS、语音识别等)

### 前端技术栈
- **框架**: Flutter 3.x - 跨平台移动应用框架
- **状态管理**: GetX - 轻量级状态管理
- **语音识别**: Sherpa-ONNX - 本地语音识别引擎
- **音频处理**: Just Audio + Flutter Sound
- **网络请求**: Dio + HTTP拦截器
- **本地存储**: SharedPreferences + SQLite

### 核心算法
- **间隔重复算法**: FSRS v4.5算法 - 更精确的记忆预测和智能调度
- **语音识别**: Sherpa-ONNX流式语音识别
- **学习分析**: 基于学习记录的智能分析
- **记忆建模**: 双参数模型 (稳定性 + 难度) 提供个性化学习体验

## 🤝 贡献指南

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

### 开发规范
- 遵循[代码规范](./docs/development/coding-standards.md)
- 编写单元测试和集成测试
- 更新相关文档
- 确保CI/CD流程通过

### 文档贡献
- 遵循[文档编写规范](./.augment/rules/doc-rule.md)
- 保持文档与代码同步
- 提供可运行的代码示例

## 📊 项目状态

### 开发进度
- ✅ 用户认证系统
- ✅ 学习卡片管理
- ✅ FSRS智能学习算法 (Flutter端完整实现)
- ✅ 语音识别功能
- ✅ 多媒体支持
- ✅ 数据同步机制
- 🔄 性能优化 (进行中)
- 🔄 UI/UX改进 (进行中)

### 技术债务
- 数据库查询优化
- 前端状态管理重构
- 测试覆盖率提升
- 文档完善

## 📄 许可证

本项目采用 [MIT License](./LICENSE) 开源协议。

## 📞 联系方式

- **项目维护者**: CheeStack团队
- **技术支持**: 通过GitHub Issues提交问题
- **功能建议**: 欢迎提交Feature Request
- **文档反馈**: 通过Pull Request改进文档

---

**注意**: 本项目正在积极开发中，API和功能可能会发生变化。建议在生产环境使用前仔细测试。
