# AI协作开发标准工作流程 (TDD主导)

## 🎯 核心理念

**TDD是整个开发流程的核心驱动力。测试用例为AI提供清晰、客观、唯一的完成标准，确保每一行代码都有明确的目的和验证方式。**

---

## 📋 第1步：需求澄清

### 🔍 需求分析要点

**明确要做什么，更重要的是明确不要做什么**

#### ✅ 必须明确的内容：
- **核心功能**：用一句话描述主要价值
- **用户场景**：谁在什么情况下使用
- **成功标准**：如何判断功能完成
- **边界条件**：什么情况下不工作是正常的

#### ❌ 必须排除的内容：
- **超出范围的功能**：明确不做什么
- **过度设计**：避免"可能需要"的功能
- **技术炫技**：拒绝不必要的复杂性

### 🎯 输出标准
- 需求描述 < 200字
- 用户故事 < 3个
- 验收标准 < 5条
- 排除清单 < 3条

---

## 🧪 第2步：TDD核心原则迭代开发

### 🎯 TDD主导地位

**TDD不仅是测试方法，更是设计方法。通过测试驱动设计，确保代码简洁、可维护、可扩展。**

### 🔄 红-绿-重构循环（小步快跑）

#### 🔴 红色阶段：写失败的测试
```
- 根据需求写一个最小的失败测试
- 测试必须明确表达期望行为
- 一次只测试一个行为点
- 确保测试确实会失败
```

#### 🟢 绿色阶段：最小可行实现
```
- 写刚好让测试通过的代码
- 不考虑优雅，只考虑正确
- 硬编码都可以，先让测试绿
- 禁止添加未测试的功能
```

#### 🔄 重构阶段：优化设计
```
- 在测试保护下改进代码
- 消除重复，提升可读性
- 优化设计模式和架构
- 每次重构后立即运行测试
```

### ⚡ 快速验证策略

**每个循环必须在30分钟内完成，超时则拆分任务**

#### TDD中的测试策略（按优先级）：

#### Flutter TDD测试策略：

**🥇 优先：单元测试驱动**
```bash
# 开发时持续监听，自动运行测试
flutter test --watch test/unit/
```
- 测试业务逻辑函数/方法
- 每个函数都要有对应测试

**🥈 其次：组件测试验证**
```bash
# 测试UI组件的行为
flutter test test/widget/login_widget_test.dart
```
- 验证Widget渲染和用户交互
- 使用`testWidgets()`和`WidgetTester`
- 模拟点击、输入、导航等操作

**🥉 最后：集成测试保障**
```bash
# 验证完整业务流程
flutter test integration_test/user_journey_test.dart
```
- 验证端到端用户流程
- 自动化脚本，不是手动操作
- 只在关键路径上使用

#### FastAPI TDD测试策略：

**🥇 优先：单元测试驱动**
```bash
# 开发时持续监听，自动运行测试
pytest-watch tests/unit/ --verbose
```
- 测试业务逻辑函数/服务类
- 每个函数都要有对应测试

**🥈 其次：API测试验证**
```bash
# 测试API端点行为
pytest tests/api/test_auth_api.py -v
```
- 验证HTTP请求响应
- 使用TestClient，不启动真实服务器
- 测试状态码、数据格式、错误处理

**🥉 最后：集成测试保障**
```bash
# 验证完整业务流程
pytest tests/integration/test_user_workflow.py -v
```
- 验证端到端业务流程
- 包含数据库操作和外部服务调用
- 只在关键业务路径上使用

### 🚨 TDD强制规则

- **测试先行**：没有测试不写代码
- **小步前进**：每次只实现一个测试
- **持续重构**：绿灯后立即优化
- **快速反馈**：每个循环 < 30分钟

### 🚫 TDD禁止行为

**绝对禁止以下行为，违反者重新开始！**

#### Flutter禁止行为：
- **❌ 禁用`flutter run`测试**：不允许启动应用手动验证功能
- **❌ 禁用手动点击验证**：所有验证必须通过自动化测试
- **❌ 禁用Postman手动测试**：API测试必须用自动化脚本

#### FastAPI禁止行为：
- **❌ 禁用`uvicorn main:app`测试**：不允许启动服务器手动验证
- **❌ 禁用浏览器访问测试**：不允许在浏览器中手动测试API
- **❌ 禁用Postman/Insomnia测试**：所有API测试必须自动化

#### 通用禁止行为：
- **❌ 禁用"先写代码后补测试"**：必须测试先行
- **❌ 禁用跳过单元测试**：不允许直接写集成测试
- **❌ 禁用"看起来能跑就行"**：必须所有测试通过才算完成

### ✅ TDD正确示例

#### 正确的Flutter TDD流程：
```dart
// 1. 先写失败的单元测试
// test/unit/user_service_test.dart
test('should login user with valid credentials', () {
  // Arrange
  final userService = UserService();

  // Act & Assert
  expect(() => userService.login('', ''), throwsException);
});

// 2. 运行测试，确保失败
// flutter test test/unit/user_service_test.dart

// 3. 写最小实现让测试通过
// lib/services/user_service.dart
class UserService {
  void login(String email, String password) {
    if (email.isEmpty || password.isEmpty) {
      throw Exception('Invalid credentials');
    }
  }
}

// 4. 运行测试，确保通过
// flutter test test/unit/user_service_test.dart

// 5. 重构优化（在测试保护下）
// 6. 继续下一个测试用例
```

#### 正确的FastAPI TDD流程：
```python
# 1. 先写失败的单元测试
# tests/unit/test_user_service.py
def test_should_create_user_with_valid_data():
    # Arrange
    user_service = UserService()
    user_data = {"email": "<EMAIL>", "password": "password123"}

    # Act & Assert
    with pytest.raises(ValidationError):
        user_service.create_user({"email": "", "password": ""})

# 2. 运行测试，确保失败
# pytest tests/unit/test_user_service.py::test_should_create_user_with_valid_data -v

# 3. 写最小实现让测试通过
# app/services/user_service.py
class UserService:
    def create_user(self, user_data: dict):
        if not user_data.get("email") or not user_data.get("password"):
            raise ValidationError("Email and password are required")
        return {"id": 1, **user_data}

# 4. 运行测试，确保通过
# pytest tests/unit/test_user_service.py::test_should_create_user_with_valid_data -v

# 5. 重构优化（在测试保护下）
# 6. 继续下一个测试用例
```

#### 正确的API测试示例：
```python
# tests/api/test_auth_endpoints.py
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_login_endpoint_with_valid_credentials():
    # Arrange
    login_data = {"email": "<EMAIL>", "password": "password123"}

    # Act
    response = client.post("/auth/login", json=login_data)

    # Assert
    assert response.status_code == 200
    assert "access_token" in response.json()

def test_login_endpoint_with_invalid_credentials():
    # Arrange
    login_data = {"email": "", "password": ""}

    # Act
    response = client.post("/auth/login", json=login_data)

    # Assert
    assert response.status_code == 422
    assert "detail" in response.json()
```

---

## 🤖 第3步：自动化验证与调整

### ⚡ 全自动化验证策略

**每完成一个TDD循环，AI自动执行完整验证流程，无需人工干预**

#### 🔧 自动化测试验证：

**🚨 严禁使用手动启动应用进行测试验证！所有测试必须通过自动化命令完成！**

##### Flutter测试三层金字塔（严格按比例）：

**📊 单元测试（70%）- 最重要**
```bash
# 测试业务逻辑，纯Dart代码，毫秒级执行
flutter test test/unit/ --coverage
```
- 测试服务类、工具类、数据模型
- 不涉及UI，不需要设备或模拟器
- 执行速度最快，最稳定可靠

**🎯 组件测试（20%）- Widget Test**
```bash
# 测试UI组件交互，使用testWidgets()
flutter test test/widget/
```
- 测试单个Widget的渲染和交互
- 模拟用户点击、输入、滑动等操作
- 在测试环境中渲染，不需要真实设备

**🔺 集成测试（10%）- 最少但必要**
```bash
# 测试完整用户流程，自动化端到端测试
flutter test integration_test/
# 或在真实设备上运行
flutter drive --driver=test_driver/integration_test.dart --target=integration_test/app_test.dart
```
- 测试完整的用户业务流程
- 自动化脚本，不是手动点击
- 在真实设备或模拟器上运行

##### 测试执行顺序（必须严格遵守）：
```bash
# 1. 单元测试（最快，优先运行）
flutter test test/unit/ --coverage

# 2. 组件测试（中等速度）
flutter test test/widget/

# 3. 集成测试（最慢，最后运行）
flutter test integration_test/

# 4. 生成测试报告
genhtml coverage/lcov.info -o coverage/html
```

##### FastAPI测试三层金字塔（严格按比例）：

**📊 单元测试（70%）- 最重要**
```bash
# 测试业务逻辑，纯Python函数，毫秒级执行
pytest tests/unit/ -v --cov=app --cov-report=html
```
- 测试服务类、工具函数、数据模型
- 不涉及HTTP请求，不需要启动服务器
- 执行速度最快，最稳定可靠

**🎯 API测试（20%）- 接口测试**
```bash
# 测试API端点，使用TestClient
pytest tests/api/ -v
```
- 测试HTTP端点的请求响应
- 使用FastAPI的TestClient，不启动真实服务器
- 验证状态码、响应数据、错误处理

**🔺 集成测试（10%）- 最少但必要**
```bash
# 测试完整业务流程，包含数据库操作
pytest tests/integration/ -v --db-test
```
- 测试完整的业务流程和数据库交互
- 使用测试数据库，不是生产数据库
- 验证端到端的数据流转

##### 测试执行顺序（必须严格遵守）：
```bash
# 1. 单元测试（最快，优先运行）
pytest tests/unit/ -v --cov=app --cov-report=term-missing

# 2. API测试（中等速度）
pytest tests/api/ -v

# 3. 集成测试（最慢，最后运行）
pytest tests/integration/ -v --db-test

# 4. 生成完整测试报告
pytest tests/ --cov=app --cov-report=html --cov-report=term
```

#### 📊 自动化代码质量检查：
- **静态分析**：pylint/dart analyze 自动检查代码质量
- **代码格式化**：black/dart format 自动格式化代码
- **安全扫描**：bandit/dart analyze 自动检查安全问题
- **依赖检查**：自动检查依赖版本和漏洞

#### ⚡ 自动化性能验证：
- **响应时间测试**：自动测试API响应时间
- **内存使用检查**：自动监控内存占用
- **并发测试**：自动测试并发场景性能
- **资源使用分析**：自动分析CPU/内存使用

#### 🎯 自动化功能验证：
- **核心功能测试**：自动运行关键业务流程
- **边界条件测试**：自动测试异常输入处理
- **错误处理验证**：自动验证异常情况响应
- **数据一致性检查**：自动验证数据完整性

### 📋 自动化验证标准

**AI必须确保以下标准全部通过才能继续：**

- **✅ 所有测试通过**：单元、集成、端到端测试全绿
- **✅ 代码质量达标**：静态分析评分 > 8.0/10
- **✅ 性能指标合格**：响应时间 < 2秒，内存使用合理
- **✅ 安全检查通过**：无高危安全漏洞
- **✅ 功能完整可用**：核心功能正常运行

### 🔧 自动化调整机制

**AI发现问题后自动执行修复，无需等待人工指令**

#### 测试失败自动修复：
```bash
# 1. 分析失败原因
flutter test --verbose test/unit/failing_test.dart

# 2. 回到TDD红-绿-重构循环
# 修复代码让测试通过

# 3. 重新运行单个测试验证
flutter test test/unit/failing_test.dart

# 4. 运行完整测试套件确保无回归
flutter test --coverage
```

#### 代码质量问题自动修复：
```
1. 自动运行代码格式化工具
2. 修复静态分析警告
3. 重构改进代码结构
4. 重新验证质量指标
```

#### 性能问题自动优化：
```
1. 识别性能瓶颈点
2. 应用常见优化策略
3. 重新测试性能指标
4. 验证优化效果
```



### 📊 测试性能基准

**如果测试执行超过以下时间，必须优化：**

#### Flutter测试性能基准：
```bash
# 单元测试性能基准
flutter test test/unit/ --coverage  # 应该 < 30秒

# 组件测试性能基准
flutter test test/widget/          # 应该 < 2分钟

# 集成测试性能基准
flutter test integration_test/     # 应该 < 5分钟
```

#### FastAPI测试性能基准：
```bash
# 单元测试性能基准
pytest tests/unit/ --cov=app       # 应该 < 20秒

# API测试性能基准
pytest tests/api/                  # 应该 < 1分钟

# 集成测试性能基准
pytest tests/integration/          # 应该 < 3分钟
```

**测试太慢的常见原因和解决方案：**

#### Flutter优化：
- 单元测试中包含了UI代码 → 拆分为纯逻辑测试
- 组件测试中包含了网络请求 → 使用Mock对象
- 集成测试覆盖了太多场景 → 减少到核心用户流程

#### FastAPI优化：
- 单元测试中包含了数据库操作 → 使用Mock对象
- API测试中启动了真实服务器 → 使用TestClient
- 集成测试中使用了生产数据库 → 使用测试数据库
- 测试中包含了外部API调用 → 使用Mock或测试替身

---

## 👥 第4步：反馈驱动优化

### 🔄 人机协作优化模式

**AI负责技术实现，人负责业务判断和优先级决策**

#### 👤 人工负责的反馈收集：
- **用户体验反馈**：真实用户的使用感受和建议
- **业务价值评估**：功能对业务目标的贡献度
- **市场需求分析**：竞品对比和市场趋势
- **优先级决策**：资源有限情况下的改进优先级

#### 🤖 AI负责的技术分析：
- **性能数据分析**：自动收集和分析性能指标
- **代码质量评估**：静态分析和技术债务识别
- **测试覆盖分析**：测试盲区和风险点识别
- **安全漏洞扫描**：自动化安全问题检测

### 📊 反馈驱动的改进流程

#### 第1阶段：反馈收集与分析（人主导）
```
1. 收集用户反馈和业务需求
2. 分析改进的业务价值和紧急程度
3. 制定改进优先级列表
4. 明确改进的验收标准
```

#### 第2阶段：技术方案设计（AI主导）
```
1. 基于反馈分析技术可行性
2. 设计具体的技术实现方案
3. 评估改进的技术风险和成本
4. 制定详细的实施计划
```

#### 第3阶段：TDD实施改进（AI执行）
```
1. 为改进点编写失败测试
2. 实现让测试通过的代码
3. 重构优化代码质量
4. 运行完整的自动化验证
```

#### 第4阶段：效果验证（人机协作）
```
1. AI：自动验证技术指标改善
2. 人：验证用户体验和业务价值
3. 共同：评估改进是否达到预期
4. 记录：总结经验和教训
```

### 📋 改进记录与追踪

**每次改进必须记录以下信息：**

- **改进触发**：什么反馈触发了这次改进
- **业务价值**：改进对用户/业务的具体价值
- **技术方案**：具体的实现方案和技术选择
- **实施过程**：TDD实施的详细步骤
- **验证结果**：技术指标和业务指标的改善
- **经验总结**：成功经验和失败教训

---

## 🚨 流程执行要求

### 🎯 质量门禁
- **第1步**：需求清晰，边界明确
- **第2步**：所有测试通过，代码可工作
- **第3步**：自动化验证全部通过
- **第4步**：改进有效，价值验证

### 📊 成功标准
- **可工作的软件**：功能正常运行
- **高质量代码**：测试覆盖，设计良好
- **快速交付**：前3步 < 2小时完成
- **持续改进**：基于真实反馈优化

---

**注意：TDD是本流程的绝对核心，任何代码都必须先有测试。第3步全自动化验证确保质量，第4步人机协作确保价值！**
