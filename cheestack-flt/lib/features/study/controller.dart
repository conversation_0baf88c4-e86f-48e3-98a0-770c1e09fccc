part of study;

class StudyController extends GetxController {
  // find this controller from to
  static StudyController get to => Get.find();

  // 新的统计数据模型
  StudyStatistics statistics = StudyStatistics();

  // 学习设置
  StudySettings settings = StudySettings();

  // 书本计划列表（保留原有功能）
  List<BookScheduleModel> bookScheduleList = [];

  // 使用工厂类创建独立的RefreshController实例
  late final RefreshController _refreshController;
  late final String _controllerKey;
  ScrollController scrollController = ScrollController();

  StudyController() {
    _controllerKey = '${runtimeType}_${hashCode}';
    _refreshController = RefreshControllerFactory.create(
      key: _controllerKey,
      initialRefresh: false,
    );
  }

  // 提供getter访问RefreshController
  RefreshController get refreshController => _refreshController;

  // 加载状态
  bool _isLoading = false;
  bool _isLoadingSettings = false;

  bool get isLoading => _isLoading;
  bool get isLoadingSettings => _isLoadingSettings;

  @override
  void onReady() async {
    super.onReady();
    await init();
  }

  @override
  void onClose() {
    // 使用工厂类释放RefreshController资源
    RefreshControllerFactory.dispose(_controllerKey);
    scrollController.dispose();
    super.onClose();
  }

  /// 初始化数据
  Future<void> init() async {
    _isLoading = true;
    update();

    try {
      // 并行加载数据
      await Future.wait([
        _loadStatistics(),
        _loadSettings(),
        _loadBookSchedules(),
      ]);
    } catch (e) {
      Console.log('初始化失败: $e');
    } finally {
      _isLoading = false;
      update();
    }
  }

  /// 加载学习统计数据 - 优先使用本地数据
  Future<void> _loadStatistics() async {
    try {
      // 优先使用本地数据服务
      if (Get.isRegistered<StudyDataService>()) {
        await _loadStatisticsFromLocal();
      } else {
        // 回退到网络API
        await _loadStatisticsFromApi();
      }
    } catch (e) {
      Console.log('加载统计数据失败: $e');
      // 使用默认值
      statistics = StudyStatistics();
    }
  }

  /// 从本地数据库加载学习统计
  Future<void> _loadStatisticsFromLocal() async {
    try {
      Console.log('📊 从本地数据库加载学习统计...');
      final studyDataService = StudyDataService.to;
      final stats = await studyDataService.getUserStudyStats();

      // 将本地统计数据转换为StudyStatistics对象
      statistics = StudyStatistics(
        todayStudied: stats['today_studied'] as int? ?? 0,
        todayReviewed: stats['today_reviewed'] as int? ?? 0,
        todayAccuracy: (stats['today_accuracy'] as num?)?.toDouble() ?? 0.0,
        consecutiveDays: stats['consecutive_days'] as int? ?? 0,
        maxConsecutiveDays: stats['max_consecutive_days'] as int? ?? 0,
        totalCards: stats['total_cards'] as int? ?? 0,
        studiedCards: stats['studied_cards'] as int? ?? 0,
        totalStudyTime: stats['total_study_time'] as int? ?? 0,
        averageAccuracy: (stats['average_accuracy'] as num?)?.toDouble() ?? 0.0,
        studyLeft: stats['study_left'] as int? ?? 0,
        reviewLeft: stats['review_left'] as int? ?? 0,
        lastStudyDate: stats['last_study_date'] != null
            ? DateTime.parse(stats['last_study_date'] as String)
            : null,
      );

      Console.log('📊 本地学习统计加载成功: ${statistics.toJson()}');
    } catch (e) {
      Console.log('❌ 从本地加载学习统计失败: $e');
      throw e;
    }
  }

  /// 从API加载学习统计（回退方案）
  Future<void> _loadStatisticsFromApi() async {
    try {
      Console.log('📊 从API加载学习统计...');
      statistics = await ApiStudyStatistics.getSimpleStatistics();
      Console.log('📊 API学习统计加载成功: ${statistics.toJson()}');
    } catch (e) {
      Console.log('❌ 从API加载学习统计失败: $e');
      throw e;
    }
  }

  /// 加载学习设置
  Future<void> _loadSettings() async {
    try {
      settings = await ApiStudyStatistics.getSettings();
    } catch (e) {
      Console.log('加载学习设置失败: $e');
      // 使用默认值
      settings = StudySettings();
    }
  }

  /// 加载书本计划列表 - 优先使用本地数据
  Future<void> _loadBookSchedules() async {
    try {
      // 优先使用本地数据服务
      if (Get.isRegistered<BookDataService>()) {
        await _loadBookSchedulesFromLocal();
      } else {
        // 回退到网络API
        await _loadBookSchedulesFromApi();
      }
    } catch (e) {
      Console.log('加载书本计划失败: $e');
      bookScheduleList = [];
    }
  }

  /// 从本地数据库加载书本计划
  Future<void> _loadBookSchedulesFromLocal() async {
    try {
      Console.log('📚 从本地数据库加载书本计划...');
      final bookDataService = BookDataService.to;

      // 获取当前用户ID
      final userId = AuthController.to.usr.user?.id;
      if (userId == null) {
        Console.log('❌ 用户未登录，无法加载书本计划');
        return;
      }

      // 获取用户的所有书籍，模拟书本计划
      final books = await bookDataService.getUserBooks(
        orderBy: 'created_at DESC',
        limit: 10, // 限制显示最近的10本书
      );

      // 将书籍转换为书本计划模型（简化版）
      bookScheduleList = books.map((book) {
        return BookScheduleModel(
          id: book.id,
          studyQty: 10, // 默认每日学习10张卡片
          studyFirst: true, // 默认优先学习新卡片
          active: true, // 假设都是激活状态
          studyLeft: 0, // 待学习数量，需要从学习记录计算
          reviewLeft: 0, // 待复习数量，需要从学习记录计算
          todayStudied: 0, // 今日已学习数量
          totalCard: 0, // 总卡片数量，需要从卡片数据计算
          totalStudiedCard: 0, // 已学习卡片数量
          userId: userId, // 使用当前用户ID
          book: book, // 关联的书籍对象
        );
      }).toList();

      Console.log('📚 本地书本计划加载成功，数量: ${bookScheduleList.length}');
    } catch (e) {
      Console.log('❌ 从本地加载书本计划失败: $e');
      throw e;
    }
  }

  /// 从API加载书本计划（回退方案）
  Future<void> _loadBookSchedulesFromApi() async {
    try {
      Console.log('📚 从API加载书本计划...');
      bookScheduleList = await ApiBookSchedule.list(
        queryParameters: {"only_unfinished": true, "active": true},
      );
      Console.log('📚 API书本计划加载成功，数量: ${bookScheduleList.length}');
    } catch (e) {
      Console.log('❌ 从API加载书本计划失败: $e');
      throw e;
    }
  }

  /// 刷新统计数据
  Future<void> fetchStudyStatistics() async {
    try {
      statistics = await ApiStudyStatistics.getSimpleStatistics();
      refreshController.refreshCompleted();
      update();
    } catch (e) {
      refreshController.refreshFailed();
      ShowToast.fail("刷新统计数据失败");
      Console.log('刷新统计数据失败: $e');
    }
  }

  /// 计算完成比例
  double calcDoneRatio(int inprocess, int total) {
    double ratio = (inprocess) / (total);
    if (ratio.isNaN) ratio = 1;
    return ratio;
  }

  /// 获取智能学习推荐
  String getStudyRecommendation() {
    return statistics.getStudyRecommendation();
  }

  /// 是否有学习任务
  bool get hasStudyTasks => statistics.hasStudyTasks;

  /// 开始智能学习
  Future<void> startSmartStudy() async {
    if (!hasStudyTasks) {
      ShowToast.text('今日学习已完成！');
      return;
    }

    // 根据设置决定学习类型
    StudyType studyType;
    int? bookId;

    if (settings.prioritizeReview && statistics.reviewLeft > 0) {
      // 优先复习
      studyType = StudyType.review;
    } else if (statistics.studyLeft > 0) {
      // 学习新卡片
      studyType = StudyType.study;
    } else {
      // 复习
      studyType = StudyType.review;
    }

    // 选择书本（如果有多本激活的书）
    final activeBooks = settings.activeBookPriorities;
    if (activeBooks.isNotEmpty) {
      bookId = activeBooks.first.bookId;
    }

    await toReviewPage(reviewType: studyType, bookId: bookId);
  }

  Future onChangeCardBook() async {
    Console.log('to卡片本列表');
    await Get.toNamed(AppRoutes.bookSearch);
    await init();
  }

  // 跳转复习页面
  /// 跳转到学习/复习页面
  Future<void> toReviewPage({
    StudyType reviewType = StudyType.review,
    int? bookId,
  }) async {
    await Get.toNamed(AppRoutes.review, arguments: {
      "type": reviewType,
      "bookId": bookId,
    });

    // 返回后刷新数据
    await init();
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    await init();
    refreshController.refreshCompleted();
  }

  /// 更新学习设置
  Future<void> updateSettings(StudySettings newSettings) async {
    _isLoadingSettings = true;
    update();

    try {
      bool success = await ApiStudyStatistics.saveSettings(newSettings);
      if (success) {
        settings = newSettings;
        ShowToast.success('设置保存成功');
      } else {
        ShowToast.fail('设置保存失败');
      }
    } catch (e) {
      ShowToast.fail('设置保存失败');
      Console.log('保存设置失败: $e');
    } finally {
      _isLoadingSettings = false;
      update();
    }
  }

  /// 跳转到设置页面（暂时使用对话框）
  Future<void> toSettingsPage() async {
    // TODO: 实现独立的设置页面
    ShowToast.text('设置功能开发中...');
  }

  /// 跳转到统计详情页面（暂时使用对话框）
  Future<void> toStatisticsPage() async {
    // TODO: 实现独立的统计页面
    ShowToast.text('详细统计功能开发中...');
  }

  /// 跳转到书本管理页面
  Future<void> toBookManagePage() async {
    await Get.toNamed(AppRoutes.bookSearch);
    // 返回后重新加载数据
    await init();
  }

  /// 获取书本计划列表（保留原有功能）
  Future<void> getBookSchedules(Map<String, dynamic> queryParameters) async {
    try {
      bookScheduleList =
          await ApiBookSchedule.list(queryParameters: queryParameters);
      update();
    } catch (e) {
      Console.log('获取书本计划失败: $e');
    }
  }

  /// 跳转到书本计划页面
  Future<void> toBookSchedulePage(int bookId) async {
    await Get.toNamed(AppRoutes.bookSchedule, arguments: bookId);
    await init();
  }
}
