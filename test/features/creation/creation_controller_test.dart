import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/features/auth/controllers/controller.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';

import 'creation_controller_test.mocks.dart';

// 生成Mock类
@GenerateMocks([
  BookDataService,
  CardDataService,
  AuthController,
  UserModel,
])
void main() {
  group('CreationController 本地数据优先测试', () {
    late CreationController controller;
    late MockBookDataService mockBookDataService;
    late MockCardDataService mockCardDataService;
    late MockAuthController mockAuthController;
    late MockUserModel mockUser;

    setUp(() {
      // 初始化Mock对象
      mockBookDataService = MockBookDataService();
      mockCardDataService = MockCardDataService();
      mockAuthController = MockAuthController();
      mockUser = MockUserModel();

      // 注册Mock服务
      Get.put<BookDataService>(mockBookDataService);
      Get.put<CardDataService>(mockCardDataService);
      Get.put<AuthController>(mockAuthController);

      // 设置用户登录状态
      when(mockUser.id).thenReturn('test_user_123');
      when(mockAuthController.usr).thenReturn(UserController());
      when(mockAuthController.usr.user).thenReturn(mockUser);

      // 创建控制器
      controller = CreationController();
    });

    tearDown(() {
      Get.reset();
    });

    group('创作统计数据加载', () {
      test('应该优先从本地数据库加载创作统计', () async {
        // Arrange - 准备测试数据
        final mockBooks = [
          BookModel(id: 1, name: '测试书籍1', createdAt: DateTime.now().toIso8601String()),
          BookModel(id: 2, name: '测试书籍2', createdAt: DateTime.now().subtract(Duration(days: 1)).toIso8601String()),
        ];
        
        final mockCards = [
          CardModel(id: 1, title: '测试卡片1', createdAt: DateTime.now()),
          CardModel(id: 2, title: '测试卡片2', createdAt: DateTime.now().subtract(Duration(hours: 2))),
          CardModel(id: 3, title: '测试卡片3', createdAt: DateTime.now().subtract(Duration(days: 2))),
        ];

        // 设置Mock返回值
        when(mockBookDataService.getUserBooks()).thenAnswer((_) async => mockBooks);
        when(mockCardDataService.getUserCards('test_user_123')).thenAnswer((_) async => mockCards);

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证结果
        expect(controller.totalBooks, equals(2));
        expect(controller.totalCards, equals(3));
        expect(controller.todayCreations, equals(2)); // 1本书 + 1张卡片
        expect(controller.weekCreations, equals(3)); // 2本书 + 1张卡片

        // 验证调用了本地服务
        verify(mockBookDataService.getUserBooks()).called(1);
        verify(mockCardDataService.getUserCards('test_user_123')).called(1);
      });

      test('当用户未登录时应该设置默认值', () async {
        // Arrange - 设置用户未登录
        when(mockAuthController.usr.user).thenReturn(null);

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证默认值
        expect(controller.totalBooks, equals(0));
        expect(controller.totalCards, equals(0));
        expect(controller.todayCreations, equals(0));
        expect(controller.weekCreations, equals(0));
      });

      test('当本地数据服务不可用时应该设置默认值', () async {
        // Arrange - 移除本地数据服务
        Get.delete<BookDataService>();
        Get.delete<CardDataService>();

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证默认值
        expect(controller.totalBooks, equals(0));
        expect(controller.totalCards, equals(0));
        expect(controller.todayCreations, equals(0));
        expect(controller.weekCreations, equals(0));
      });
    });

    group('最近创作数据加载', () {
      test('应该优先从本地数据库加载最近创作', () async {
        // Arrange - 准备测试数据
        final mockCards = [
          CardModel(
            id: 1, 
            title: '最新卡片1', 
            question: '问题1',
            createdAt: DateTime.now(),
          ),
          CardModel(
            id: 2, 
            title: '最新卡片2', 
            question: '问题2',
            createdAt: DateTime.now().subtract(Duration(hours: 1)),
          ),
          CardModel(
            id: 3, 
            title: '最新卡片3', 
            question: '问题3',
            createdAt: DateTime.now().subtract(Duration(hours: 2)),
          ),
        ];

        // 设置Mock返回值
        when(mockCardDataService.getUserCards(
          'test_user_123',
          orderBy: 'created_at DESC',
          limit: 3,
        )).thenAnswer((_) async => mockCards);

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证结果
        expect(controller.recentCreations.length, equals(3));
        expect(controller.recentCreations[0]['title'], equals('最新卡片1'));
        expect(controller.recentCreations[0]['subtitle'], equals('问题1'));
        expect(controller.recentCreations[0]['type'], equals('card'));

        // 验证调用了本地服务
        verify(mockCardDataService.getUserCards(
          'test_user_123',
          orderBy: 'created_at DESC',
          limit: 3,
        )).called(1);
      });

      test('应该正确格式化时间显示', () async {
        // Arrange - 准备不同时间的测试数据
        final now = DateTime.now();
        final mockCards = [
          CardModel(
            id: 1, 
            title: '刚刚的卡片', 
            createdAt: now,
          ),
          CardModel(
            id: 2, 
            title: '30分钟前的卡片', 
            createdAt: now.subtract(Duration(minutes: 30)),
          ),
          CardModel(
            id: 3, 
            title: '2小时前的卡片', 
            createdAt: now.subtract(Duration(hours: 2)),
          ),
        ];

        when(mockCardDataService.getUserCards(
          'test_user_123',
          orderBy: 'created_at DESC',
          limit: 3,
        )).thenAnswer((_) async => mockCards);

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证时间格式化
        expect(controller.recentCreations[0]['time'], equals('刚刚'));
        expect(controller.recentCreations[1]['time'], equals('30分钟前'));
        expect(controller.recentCreations[2]['time'], equals('2小时前'));
      });
    });
  });
}
