import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

void main() {
  group('创作页面本地数据优先测试', () {
    setUp(() {
      // 重置GetX状态
      Get.reset();
    });

    tearDown(() {
      Get.reset();
    });

    group('创作统计数据本地化', () {
      test('应该优先从本地数据库加载创作统计', () async {
        // Arrange - 准备测试数据
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);

        final mockBooks = [
          BookModel(id: 1, name: '今日书籍', createdAt: now.toIso8601String()),
          BookModel(
            id: 2,
            name: '昨日书籍',
            createdAt: now.subtract(Duration(days: 1)).toIso8601String(),
          ),
        ];

        final mockCards = [
          CardModel(id: 1, title: '今日卡片1', createdAt: now),
          CardModel(
            id: 2,
            title: '今日卡片2',
            createdAt: now.subtract(Duration(hours: 2)),
          ),
          CardModel(
            id: 3,
            title: '昨日卡片',
            createdAt: now.subtract(Duration(days: 1)),
          ),
        ];

        // 设置Mock返回值
        when(
          mockBookDataService.getUserBooks(),
        ).thenAnswer((_) async => mockBooks);
        when(
          mockCardDataService.getUserCards('test_user_123'),
        ).thenAnswer((_) async => mockCards);

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证结果
        expect(controller.totalBooks, equals(2), reason: '应该统计所有书籍数量');
        expect(controller.totalCards, equals(3), reason: '应该统计所有卡片数量');
        expect(
          controller.todayCreations,
          equals(3),
          reason: '应该统计今日创作数量：1本书+2张卡片',
        );
        expect(
          controller.weekCreations,
          equals(4),
          reason: '应该统计本周创作数量：2本书+2张卡片',
        );

        // 验证调用了本地服务
        verify(mockBookDataService.getUserBooks()).called(1);
        verify(mockCardDataService.getUserCards('test_user_123')).called(1);
      });

      test('当本地数据服务不可用时应该设置默认值', () async {
        // Arrange - 移除本地数据服务
        Get.delete<BookDataService>();
        Get.delete<CardDataService>();

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证默认值
        expect(controller.totalBooks, equals(0));
        expect(controller.totalCards, equals(0));
        expect(controller.todayCreations, equals(0));
        expect(controller.weekCreations, equals(0));
      });
    });

    group('最近创作数据本地化', () {
      test('应该优先从本地数据库加载最近创作', () async {
        // Arrange - 准备测试数据
        final now = DateTime.now();
        final mockCards = [
          CardModel(id: 1, title: '最新卡片1', question: '问题1', createdAt: now),
          CardModel(
            id: 2,
            title: '最新卡片2',
            question: '问题2',
            createdAt: now.subtract(Duration(hours: 1)),
          ),
          CardModel(
            id: 3,
            title: '最新卡片3',
            question: '问题3',
            createdAt: now.subtract(Duration(hours: 2)),
          ),
        ];

        // 设置Mock返回值
        when(
          mockCardDataService.getUserCards(
            'test_user_123',
            orderBy: 'created_at DESC',
            limit: 3,
          ),
        ).thenAnswer((_) async => mockCards);

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证结果
        expect(controller.recentCreations.length, equals(3));
        expect(controller.recentCreations[0]['title'], equals('最新卡片1'));
        expect(controller.recentCreations[0]['subtitle'], equals('问题1'));
        expect(controller.recentCreations[0]['type'], equals('card'));

        // 验证调用了本地服务
        verify(
          mockCardDataService.getUserCards(
            'test_user_123',
            orderBy: 'created_at DESC',
            limit: 3,
          ),
        ).called(1);
      });

      test('应该正确格式化时间显示', () async {
        // Arrange - 准备不同时间的测试数据
        final now = DateTime.now();
        final mockCards = [
          CardModel(
            id: 1,
            title: '刚刚的卡片',
            createdAt: now.subtract(Duration(seconds: 30)),
          ),
          CardModel(
            id: 2,
            title: '30分钟前的卡片',
            createdAt: now.subtract(Duration(minutes: 30)),
          ),
          CardModel(
            id: 3,
            title: '2小时前的卡片',
            createdAt: now.subtract(Duration(hours: 2)),
          ),
        ];

        when(
          mockCardDataService.getUserCards(
            'test_user_123',
            orderBy: 'created_at DESC',
            limit: 3,
          ),
        ).thenAnswer((_) async => mockCards);

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证时间格式化
        expect(controller.recentCreations[0]['time'], equals('刚刚'));
        expect(controller.recentCreations[1]['time'], equals('30分钟前'));
        expect(controller.recentCreations[2]['time'], equals('2小时前'));
      });
    });

    group('错误处理', () {
      test('当加载数据失败时应该设置默认值', () async {
        // Arrange - 设置Mock抛出异常
        when(
          mockBookDataService.getUserBooks(),
        ).thenThrow(Exception('数据库连接失败'));
        when(
          mockCardDataService.getUserCards(any),
        ).thenThrow(Exception('数据库连接失败'));

        // Act - 执行测试
        await controller.loadCreationStats();

        // Assert - 验证默认值
        expect(controller.totalBooks, equals(0));
        expect(controller.totalCards, equals(0));
        expect(controller.todayCreations, equals(0));
        expect(controller.weekCreations, equals(0));
        expect(controller.recentCreations, isEmpty);
      });
    });
  });
}

// Mock类需要手动创建，因为mockito生成可能有问题
class MockBookDataService extends Mock implements BookDataService {}

class MockCardDataService extends Mock implements CardDataService {}
